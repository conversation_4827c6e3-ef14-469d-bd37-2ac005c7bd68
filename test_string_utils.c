#include <stdio.h>
#include <assert.h>
#include "string_utils.h"

void test_string_length() {
    printf("Testing string_length function...\n");

    // Test normal cases
    assert(string_length("hello") == 5);
    assert(string_length("world") == 5);
    assert(string_length("a") == 1);
    assert(string_length("testing123") == 10);

    // Test empty string
    assert(string_length("") == 0);

    // Test NULL pointer
    assert(string_length(NULL) == 0);

    // Test string with spaces
    assert(string_length("hello world") == 11);
    assert(string_length("   ") == 3);

    // Test string with special characters
    assert(string_length("hello\n") == 6);
    assert(string_length("tab\there") == 8);

    printf("string_length tests passed!\n");
}

void test_string_copy() {
    printf("Testing string_copy function...\n");

    char dest[100];

    // Test normal copy
    assert(string_copy(dest, "hello", 100) == dest);
    assert(string_compare(dest, "hello") == 0);

    // Test copy empty string
    assert(string_copy(dest, "", 100) == dest);
    assert(string_compare(dest, "") == 0);

    // Test copy single character
    assert(string_copy(dest, "a", 100) == dest);
    assert(string_compare(dest, "a") == 0);

    // Test exact size buffer
    char small_dest[6];
    assert(string_copy(small_dest, "hello", 6) == small_dest);
    assert(string_compare(small_dest, "hello") == 0);

    // Test insufficient buffer size
    char tiny_dest[3];
    assert(string_copy(tiny_dest, "hello", 3) == NULL);

    // Test NULL pointers
    assert(string_copy(NULL, "hello", 100) == NULL);
    assert(string_copy(dest, NULL, 100) == NULL);
    assert(string_copy(NULL, NULL, 100) == NULL);

    // Test zero size buffer
    assert(string_copy(dest, "hello", 0) == NULL);

    // Test buffer size exactly one less than needed
    assert(string_copy(dest, "hello", 5) == NULL);  // needs 6 for null terminator

    printf("string_copy tests passed!\n");
}

void test_string_concatenate() {
    printf("Testing string_concatenate function...\n");

    char dest[100];

    // Test normal concatenation
    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, " world", 100) == dest);
    assert(string_compare(dest, "hello world") == 0);

    // Test concatenate to empty string
    string_copy(dest, "", 100);
    assert(string_concatenate(dest, "hello", 100) == dest);
    assert(string_compare(dest, "hello") == 0);

    // Test concatenate empty string
    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, "", 100) == dest);
    assert(string_compare(dest, "hello") == 0);

    // Test concatenate single character
    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, "!", 100) == dest);
    assert(string_compare(dest, "hello!") == 0);

    // Test exact size buffer
    char exact_dest[12];
    string_copy(exact_dest, "hello", 12);
    assert(string_concatenate(exact_dest, " world", 12) == exact_dest);
    assert(string_compare(exact_dest, "hello world") == 0);

    // Test insufficient buffer size
    char small_dest[8];
    string_copy(small_dest, "hello", 8);
    assert(string_concatenate(small_dest, " world", 8) == NULL);

    // Test NULL pointers
    assert(string_concatenate(NULL, "hello", 100) == NULL);
    assert(string_concatenate(dest, NULL, 100) == NULL);
    assert(string_concatenate(NULL, NULL, 100) == NULL);

    // Test zero size buffer
    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, " world", 0) == NULL);

    printf("string_concatenate tests passed!\n");
}

void test_string_compare() {
    printf("Testing string_compare function...\n");

    // Test equal strings
    assert(string_compare("hello", "hello") == 0);
    assert(string_compare("", "") == 0);
    assert(string_compare("a", "a") == 0);
    assert(string_compare("test123", "test123") == 0);

    // Test first string lexicographically smaller
    assert(string_compare("abc", "abd") < 0);
    assert(string_compare("hello", "world") < 0);
    assert(string_compare("a", "b") < 0);
    assert(string_compare("", "a") < 0);
    assert(string_compare("abc", "abcd") < 0);

    // Test first string lexicographically larger
    assert(string_compare("abd", "abc") > 0);
    assert(string_compare("world", "hello") > 0);
    assert(string_compare("b", "a") > 0);
    assert(string_compare("a", "") > 0);
    assert(string_compare("abcd", "abc") > 0);

    // Test case sensitivity
    assert(string_compare("Hello", "hello") < 0);  // 'H' < 'h' in ASCII
    assert(string_compare("HELLO", "hello") < 0);

    // Test with numbers
    assert(string_compare("123", "124") < 0);
    assert(string_compare("124", "123") > 0);

    // Test NULL pointers
    assert(string_compare(NULL, NULL) == 0);
    assert(string_compare(NULL, "hello") > 0);  // NULL is considered "greater"
    assert(string_compare("hello", NULL) < 0);  // non-NULL is considered "less"

    // Test strings with special characters
    assert(string_compare("hello\n", "hello\t") > 0);  // '\n' > '\t' in ASCII

    printf("string_compare tests passed!\n");
}

int main() {
    printf("Starting unit tests for string utilities...\n\n");

    test_string_length();
    printf("\n");

    test_string_copy();
    printf("\n");

    test_string_concatenate();
    printf("\n");

    test_string_compare();
    printf("\n");

    printf("All tests passed!\n");

    return 0;
}