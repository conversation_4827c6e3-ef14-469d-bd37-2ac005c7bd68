#include <stdio.h>
#include <assert.h>
#include "string_utils.h"

void test_string_length() {
    printf("Testing string_length function...\n");

    assert(string_length("hello") == 5);
    assert(string_length("world") == 5);
    assert(string_length("a") == 1);
    assert(string_length("testing123") == 10);

    assert(string_length("") == 0);

    assert(string_length(NULL) == 0);

    assert(string_length("hello world") == 11);
    assert(string_length("   ") == 3);

    assert(string_length("hello\n") == 6);
    assert(string_length("tab\there") == 8);

    printf("string_length tests passed!\n");
}

void test_string_copy() {
    printf("Testing string_copy function...\n");

    char dest[100];

    assert(string_copy(dest, "hello", 100) == dest);
    assert(string_compare(dest, "hello") == 0);

    assert(string_copy(dest, "", 100) == dest);
    assert(string_compare(dest, "") == 0);

    assert(string_copy(dest, "a", 100) == dest);
    assert(string_compare(dest, "a") == 0);

    char small_dest[6];
    assert(string_copy(small_dest, "hello", 6) == small_dest);
    assert(string_compare(small_dest, "hello") == 0);

    char tiny_dest[3];
    assert(string_copy(tiny_dest, "hello", 3) == NULL);

    assert(string_copy(NULL, "hello", 100) == NULL);
    assert(string_copy(dest, NULL, 100) == NULL);
    assert(string_copy(NULL, NULL, 100) == NULL);

    assert(string_copy(dest, "hello", 0) == NULL);

    assert(string_copy(dest, "hello", 5) == NULL);

    printf("string_copy tests passed!\n");
}

void test_string_concatenate() {
    printf("Testing string_concatenate function...\n");

    char dest[100];

    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, " world", 100) == dest);
    assert(string_compare(dest, "hello world") == 0);

    string_copy(dest, "", 100);
    assert(string_concatenate(dest, "hello", 100) == dest);
    assert(string_compare(dest, "hello") == 0);

    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, "", 100) == dest);
    assert(string_compare(dest, "hello") == 0);

    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, "!", 100) == dest);
    assert(string_compare(dest, "hello!") == 0);

    char exact_dest[12];
    string_copy(exact_dest, "hello", 12);
    assert(string_concatenate(exact_dest, " world", 12) == exact_dest);
    assert(string_compare(exact_dest, "hello world") == 0);

    char small_dest[8];
    string_copy(small_dest, "hello", 8);
    assert(string_concatenate(small_dest, " world", 8) == NULL);

    assert(string_concatenate(NULL, "hello", 100) == NULL);
    assert(string_concatenate(dest, NULL, 100) == NULL);
    assert(string_concatenate(NULL, NULL, 100) == NULL);

    string_copy(dest, "hello", 100);
    assert(string_concatenate(dest, " world", 0) == NULL);

    printf("string_concatenate tests passed!\n");
}

void test_string_compare() {
    printf("Testing string_compare function...\n");

    assert(string_compare("hello", "hello") == 0);
    assert(string_compare("", "") == 0);
    assert(string_compare("a", "a") == 0);
    assert(string_compare("test123", "test123") == 0);

    assert(string_compare("abc", "abd") < 0);
    assert(string_compare("hello", "world") < 0);
    assert(string_compare("a", "b") < 0);
    assert(string_compare("", "a") < 0);
    assert(string_compare("abc", "abcd") < 0);

    assert(string_compare("abd", "abc") > 0);
    assert(string_compare("world", "hello") > 0);
    assert(string_compare("b", "a") > 0);
    assert(string_compare("a", "") > 0);
    assert(string_compare("abcd", "abc") > 0);

    assert(string_compare("Hello", "hello") < 0);
    assert(string_compare("HELLO", "hello") < 0);

    assert(string_compare("123", "124") < 0);
    assert(string_compare("124", "123") > 0);

    assert(string_compare(NULL, NULL) == 0);
    assert(string_compare(NULL, "hello") > 0);
    assert(string_compare("hello", NULL) < 0);

    assert(string_compare("hello\n", "hello\t") > 0);

    printf("string_compare tests passed!\n");
}

int main() {
    printf("Starting unit tests for string utilities...\n\n");

    test_string_length();
    printf("\n");

    test_string_copy();
    printf("\n");

    test_string_concatenate();
    printf("\n");

    test_string_compare();
    printf("\n");

    printf("All tests passed!\n");

    return 0;
}