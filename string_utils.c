#include "string_utils.h"

/* This function returns the length of the string. */
int string_length(const char *str) {
    if (str == NULL) {
        return 0;
    }
    int len = 0;
    while (*str != '\0') {
        len++;
        str++;
    }
    return len;
}

/* This function copies the string from src to dest. */
char *string_copy(char *dest, const char *src, size_t dest_size) {
    if (dest == NULL || src == NULL || dest_size < (string_length(src)+1)) { 
        return NULL;
    }
    char *ptr = dest;
    while (*src != '\0') {
        *ptr = *src;
        ptr++;
        src++;
    }
    *ptr = '\0';
    return dest;
}

/* This function concatenates string src to the end of dest. */
char *string_concatenate(char *dest, const char *src, size_t dest_size) {
    if (dest == NULL || src == NULL || dest_size < (string_length(dest) + string_length(src) + 1)) {
        return NULL;
    }
    char *ptr = dest;
    while (*ptr != '\0') {
        ptr++;
    }
    while (*src != '\0') {
        *ptr = *src;
        ptr++;
        src++;
    }
    *ptr = '\0';
    return dest;
}

/* This function compares strings str1 and str2. It returns 0 if the strings are equal, >0 if str1 > str2 and <0 if str2 > str1 */
int string_compare(const char *str1, const char *str2) {
    if (str1 == NULL || str2 == NULL) {
        return (str1 == NULL) - (str2 == NULL);
    }
    while (*str1 != '\0' && *str2 != '\0' && *str1 == *str2) {
        str1++;
        str2++;
    }
    return *str1 - *str2;
}