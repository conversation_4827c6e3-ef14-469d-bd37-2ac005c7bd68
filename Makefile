CC=gcc
CFLAGS= -Wall -g

all : mygrep data-extract data-gen test_string_utils

mygrep: mygrep.c
	$(CC) $(CFLAGS) -o $@ $<

data-gen: data-gen.c
	$(CC) $(CFLAGS) -o $@ $<

data-extract: data-extract.c 
	$(CC) $(CFLAGS) -o $@ $<

test_string_utils: string_utils.c test_string_utils.c string_utils.h 
	$(CC) $(CFLAGS) -o $@ string_utils.c test_string_utils.c

clean: 
	rm -rf mygrep data-extract data-gen test_string_utils *.o
